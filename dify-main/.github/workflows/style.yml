name: Style check

on:
  pull_request:
    branches:
      - main

concurrency:
  group: style-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

permissions:
  checks: write
  statuses: write
  contents: read


jobs:
  python-style:
    name: Python Style
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Check changed files
        id: changed-files
        uses: tj-actions/changed-files@v45
        with:
          files: |
            api/**
            .github/workflows/style.yml

      - name: Setup UV and Python
        if: steps.changed-files.outputs.any_changed == 'true'
        uses: ./.github/actions/setup-uv
        with:
          uv-lockfile: api/uv.lock
          enable-cache: false

      - name: Install dependencies
        if: steps.changed-files.outputs.any_changed == 'true'
        run: uv sync --project api --dev

      - name: Ruff check
        if: steps.changed-files.outputs.any_changed == 'true'
        run: |
          uv run --directory api ruff --version
          uv run --directory api ruff check --diff ./
          uv run --directory api ruff format --check --diff ./

      - name: Dotenv check
        if: steps.changed-files.outputs.any_changed == 'true'
        run: uv run --project api dotenv-linter ./api/.env.example ./web/.env.example

      - name: Lint hints
        if: failure()
        run: echo "Please run 'dev/reformat' to fix the fixable linting errors."

  web-style:
    name: Web Style
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./web

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Check changed files
        id: changed-files
        uses: tj-actions/changed-files@v45
        with:
          files: web/**

      - name: Install pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10
          run_install: false

      - name: Setup NodeJS
        uses: actions/setup-node@v4
        if: steps.changed-files.outputs.any_changed == 'true'
        with:
          node-version: 22
          cache: pnpm
          cache-dependency-path: ./web/package.json

      - name: Web dependencies
        if: steps.changed-files.outputs.any_changed == 'true'
        run: pnpm install --frozen-lockfile

      - name: Web style check
        if: steps.changed-files.outputs.any_changed == 'true'
        run: pnpm run lint

  docker-compose-template:
    name: Docker Compose Template
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          persist-credentials: false

      - name: Check changed files
        id: changed-files
        uses: tj-actions/changed-files@v45
        with:
          files: |
            docker/generate_docker_compose
            docker/.env.example
            docker/docker-compose-template.yaml
            docker/docker-compose.yaml

      - name: Generate Docker Compose
        if: steps.changed-files.outputs.any_changed == 'true'
        run: |
          cd docker
          ./generate_docker_compose

      - name: Check for changes
        if: steps.changed-files.outputs.any_changed == 'true'
        run: git diff --exit-code

  superlinter:
    name: SuperLinter
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          persist-credentials: false

      - name: Check changed files
        id: changed-files
        uses: tj-actions/changed-files@v45
        with:
          files: |
            **.sh
            **.yaml
            **.yml
            **Dockerfile
            dev/**

      - name: Super-linter
        uses: super-linter/super-linter/slim@v7
        if: steps.changed-files.outputs.any_changed == 'true'
        env:
          BASH_SEVERITY: warning
          DEFAULT_BRANCH: main
          FILTER_REGEX_INCLUDE: pnpm-lock.yaml
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          IGNORE_GENERATED_FILES: true
          IGNORE_GITIGNORED_FILES: true
          VALIDATE_BASH: true
          VALIDATE_BASH_EXEC: true
          # FIXME: temporarily disabled until api-docker.yaml's run script is fixed for shellcheck
          # VALIDATE_GITHUB_ACTIONS: true
          VALIDATE_DOCKERFILE_HADOLINT: true
          VALIDATE_XML: true
          VALIDATE_YAML: true

      - name: EditorConfig checks
        uses: super-linter/super-linter/slim@v7
        env:
          DEFAULT_BRANCH: main
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          IGNORE_GENERATED_FILES: true
          IGNORE_GITIGNORED_FILES: true
          # EditorConfig validation
          VALIDATE_EDITORCONFIG: true
          EDITORCONFIG_FILE_NAME: editorconfig-checker.json
