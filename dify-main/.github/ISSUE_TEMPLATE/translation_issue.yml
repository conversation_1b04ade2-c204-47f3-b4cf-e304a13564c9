name: "🌐 Localization/Translation issue"
description: Report incorrect translations. [please use English :)]
labels:
  - translation
body:
  - type: checkboxes
    attributes:
      label: Self Checks
      description: "To make sure we get to you in time, please check the following :)"
      options:
        - label: I have searched for existing issues [search for existing issues](https://github.com/langgenius/dify/issues), including closed ones.
          required: true
        - label: I confirm that I am using English to submit this report (我已阅读并同意 [Language Policy](https://github.com/langgenius/dify/issues/1542)).
          required: true
        - label: "[FOR CHINESE USERS] 请务必使用英文提交 Issue，否则会被关闭。谢谢！:)"
          required: true
        - label: "Please do not modify this template :) and fill in all the required fields."
          required: true
  - type: input
    attributes:
      label: Dify version
      description: Hover over system tray icon or look at Settings
    validations:
      required: true
  - type: input
    attributes:
      label: Utility with translation issue
      placeholder: Some area
      description: Please input here the utility with the translation issue
    validations:
      required: true
  - type: input
    attributes:
      label: 🌐 Language affected
      placeholder: "German"
    validations:
      required: true
  - type: textarea
    attributes:
      label: ❌ Actual phrase(s)
      placeholder: What is there? Please include a screenshot as that is extremely helpful.
    validations:
      required: true
  - type: textarea
    attributes:
      label: ✔️ Expected phrase(s)
      placeholder: What was expected?
    validations:
      required: true
  - type: textarea
    attributes:
      label: ℹ Why is the current translation wrong
      placeholder: Why do you feel this is incorrect?
    validations:
      required: true
