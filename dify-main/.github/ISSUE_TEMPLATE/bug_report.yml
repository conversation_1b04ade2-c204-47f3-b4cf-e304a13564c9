name: "🕷️ Bug report"
description: Report errors or unexpected behavior
labels:
  - bug
body:
  - type: checkboxes
    attributes:
      label: Self Checks
      description: "To make sure we get to you in time, please check the following :)"
      options:
        - label: This is only for bug report, if you would like to ask a question, please head to [Discussions](https://github.com/langgenius/dify/discussions/categories/general).
          required: true
        - label: I have searched for existing issues [search for existing issues](https://github.com/langgenius/dify/issues), including closed ones.
          required: true
        - label: I confirm that I am using English to submit this report (我已阅读并同意 [Language Policy](https://github.com/langgenius/dify/issues/1542)).
          required: true
        - label: "[FOR CHINESE USERS] 请务必使用英文提交 Issue，否则会被关闭。谢谢！:)"
          required: true
        - label: "Please do not modify this template :) and fill in all the required fields."
          required: true

  - type: input
    attributes:
      label: Dify version
      description: See about section in Dify console
    validations:
      required: true

  - type: dropdown
    attributes:
      label: Cloud or Self Hosted
      description: How / Where was Dify installed from?
      multiple: true
      options:
        - Cloud
        - Self Hosted (Docker)
        - Self Hosted (Source)
    validations:
      required: true

  - type: textarea
    attributes:
      label: Steps to reproduce
      description: We highly suggest including screenshots and a bug report log. Please use the right markdown syntax for code blocks.
      placeholder: Having detailed steps helps us reproduce the bug.
    validations:
      required: true

  - type: textarea
    attributes:
      label: ✔️ Expected Behavior
      placeholder: What were you expecting?
    validations:
      required: false

  - type: textarea
    attributes:
      label: ❌ Actual Behavior
      placeholder: What happened instead?
    validations:
      required: false
